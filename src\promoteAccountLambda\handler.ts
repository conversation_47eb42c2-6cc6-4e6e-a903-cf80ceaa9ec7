import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { verifyToken } from '../shared/utils/tokenUtils';
import { getUserById, promoteChildToParent } from '../shared/database/userOperations';
import { ErrorResponse, SuccessResponse } from '../shared/responseUtils';
import { getConfig } from '../shared/services/configService';
import { addCorsHeaders } from '../shared/corsHandler';

export const promoteAccountLambda = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  // Load configuration from the service
  const config = await getConfig(event);
  
  

  

  

  console.log('[PROMOTE-ACCOUNT] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });
  
  console.log('[CONFIG] PromoteAccountLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    frontendUrl: config.FRONTEND_URL,
    userTable: config.USER_DETAILS_TABLE_NAME
  });

  try {
    // Verify JWT token
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) {
      return ErrorResponse(401, 'UNAUTHORIZED', 'Authorization header is required');
    }

    const token = authHeader.replace('Bearer ', '');
    const decodedToken = await verifyToken(token, config.USER_DETAILS_TABLE_NAME);

    if (!decodedToken) {
      return ErrorResponse(401, 'INVALID_TOKEN', 'Invalid or expired token');
    }
    
    // Get user from database
    const user = await getUserById(decodedToken.userID, config.USER_DETAILS_TABLE_NAME);
    
    if (!user) {
      return ErrorResponse(404, 'USER_NOT_FOUND', 'User not found');
    }
    
    // Check if user is eligible for promotion
    if (user.accountType !== 'child') {
      return ErrorResponse(400, 'INVALID_ACCOUNT_TYPE', 'Only child accounts can be promoted');
    }
    
    // Optional: Check if this is a special child account that can be promoted
    // For example, only promote accounts under a specific parent
    // if (user.parentAccount !== 'APK_authiqa_parent_key') {
    //   return ErrorResponse(403, 'NOT_ELIGIBLE', 'This account is not eligible for promotion');
    // }
    
    // Promote the account
    await promoteChildToParent(user.userID, config.USER_DETAILS_TABLE_NAME);
    
    return addCorsHeaders(SuccessResponse(200, {
      message: 'Account successfully promoted to parent account',
      userID: user.userID,
      email: user.email
    }), event);
  } catch (error: any) {
    console.error('Error promoting account:', error);
    return addCorsHeaders(ErrorResponse(
      500,
      'PROMOTION_ERROR',
      error.message || 'An error occurred while promoting the account'
    ), event);
  }
};


   