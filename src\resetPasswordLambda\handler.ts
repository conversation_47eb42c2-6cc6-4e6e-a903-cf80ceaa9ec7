import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorTypes } from '../shared/errorTypes';
import { ErrorResponse, SuccessResponse } from '../shared/responseUtils';
import { getUserByEmail, updateUserResetPasswordOTP, getUserByEmailAndParent, getUserByPublicKey, incrementResetPasswordRequestCount } from '../shared/database/userOperations';
import { generateOTP } from '../shared/otp';
import { validateEmail } from '../shared/validation/userValidation';
import { OTP_EXPIRY_TIME, OTP_RESEND_COOLDOWN } from '../shared/constants';
import { addCorsHeaders } from '../shared/corsHandler';
import { generateResetToken } from '../shared/utils/tokenUtils';
import { sendPasswordResetEmail } from '../shared/emailService';
import { isValidPublicKeyFormat } from '../shared/utils/apiKeyUtils';
import { getConfig } from '../shared/services/configService';

interface ResetPasswordRequest {
  email: string;
  parentPublicKey?: string;
  updatePasswordPath?: string;
}

/**
 * Validates the request body and extracts the required fields
 */
function validateRequestBody(body: string | null): ResetPasswordRequest {
  if (!body) {
    throw ErrorResponse(400, 'MISSING_REQUEST_BODY', 'Request body is required');
  }

  let requestBody;
  try {
    requestBody = JSON.parse(body);
  } catch {
    throw ErrorResponse(400, 'INVALID_REQUEST_BODY', 'Invalid JSON in request body');
  }

  const { email, parentPublicKey, updatePasswordPath } = requestBody;
  if (!email) {
    throw ErrorResponse(400, 'MISSING_REQUIRED_FIELDS', 'Email is required');
  }

  // Validate email format
  const emailValidation = validateEmail(email);
  if (!emailValidation.isValid) {
    throw ErrorResponse(400, 'INVALID_EMAIL_FORMAT', 'Invalid email format');
  }

  return { email, parentPublicKey, updatePasswordPath };
}

/**
 * Retrieves the user based on email and optional parent public key
 */
async function getUser(email: string, parentPublicKey?: string): Promise<any> {
  let user;
  
  if (parentPublicKey) {
    // Validate parentPublicKey format first
    if (!isValidPublicKeyFormat(parentPublicKey)) {
      throw ErrorResponse(400, 'INVALID_PARENT_PUBLIC_KEY_FORMAT', 'Invalid public key format');
    }
    
    // Use getUserByEmailAndParent instead of getUserByEmail
    user = await getUserByEmailAndParent(email, parentPublicKey);
  } else {
    user = await getUserByEmail(email);
  }

  if (!user) {
    throw ErrorResponse(404, 'USER_NOT_FOUND', 'User not found');
  }

  return user;
}

/**
 * Validates account type specific requirements
 */
async function validateAccountRequirements(user: any, parentPublicKey?: string, tableName?: string) {
  // Check email verification
  if (!user.emailVerified) {
    throw ErrorResponse(403, 'EMAIL_NOT_VERIFIED', 'Email must be verified before requesting password reset');
  }

  // Add parent public key validation for child accounts
  if (user.accountType === 'child') {
    if (!parentPublicKey) {
      throw ErrorResponse(400, 'MISSING_PARENT_PUBLIC_KEY', 'Parent public key is required for child accounts');
    }
    if (user.parentAccount !== parentPublicKey) {
      throw ErrorResponse(401, 'INVALID_PARENT_PUBLIC_KEY', 'Invalid parent public key');
    }
    const parentUser = await getUserByPublicKey(user.parentAccount, tableName);
    if (!parentUser || parentUser.availableBalance === undefined || parentUser.availableBalance <= 0) {
      throw ErrorResponse(403, 'PARENT_ACCOUNT_INACTIVE', 'Parent account has insufficient balance for child operations');
    }
  } else {
    if (parentPublicKey) {
      throw ErrorResponse(400, 'INVALID_REQUEST', 'Parent public key should not be provided for parent accounts');
    }
    if (user.parentAccount !== 'ROOT') {
      throw ErrorResponse(401, 'INVALID_CREDENTIALS', 'Invalid credentials');
    }
  }

  // Check account status
  if (user.accountStatus === 'inactive') {
    throw ErrorResponse(403, 'ACCOUNT_INACTIVE', 'Your account is not active');
  }

  if (user.accountStatus === 'locked') {
    throw ErrorResponse(403, 'ACCOUNT_LOCKED', 'Your account has been locked');
  }
}

/**
 * Checks rate limiting for password reset requests
 */
function checkRateLimiting(user: any) {
  const lastResetAt = user.lastResetPasswordRequestAt || 0;
  const timeSinceLastReset = Date.now() - lastResetAt;
  
  if (timeSinceLastReset < OTP_RESEND_COOLDOWN) {
    const remainingTime = Math.ceil((OTP_RESEND_COOLDOWN - timeSinceLastReset) / 1000);
    throw ErrorResponse(429, 'RATE_LIMIT_EXCEEDED', 
      `Please wait ${remainingTime} seconds before requesting another reset link`);
  }
}

/**
 * Generates the reset link based on account type and provided paths
 */
async function generateResetLink(
  user: any, 
  resetToken: string, 
  parentPublicKey?: string, 
  updatePasswordPath?: string,
  event?: APIGatewayProxyEvent
): Promise<string> {
  // If it's a child account and we have a parent public key
  if (user.accountType === 'child' && parentPublicKey) {
    try {
      const parentUser = await getUserByPublicKey(parentPublicKey);
      
      if (!parentUser) {
        throw new Error('Parent user not found');
      }
      
      if (updatePasswordPath) {
        // Don't concatenate full URLs - check if updatePasswordPath is a full URL
        if (updatePasswordPath.startsWith('http')) {
          return `${updatePasswordPath}?token=${resetToken}`;
        }
        // Otherwise, properly join the paths
        const baseUrl = parentUser.organizationUrl?.replace(/\/+$/, '');
        const cleanPath = updatePasswordPath.startsWith('/') ? updatePasswordPath : `/${updatePasswordPath}`;
        return `${baseUrl}${cleanPath}?token=${resetToken}`;
      }
    } catch (error) {
      console.error('Error getting parent user:', error);
    }
  }
  
  // Get frontend URL from config service
  let baseUrl = 'https://authiqa.com';
  if (event) {
    try {
      const config = await getConfig(event);
      baseUrl = config.FRONTEND_URL;
      console.log(`Using frontend URL from config service: ${baseUrl}`);
    } catch (error) {
      console.error('Error getting frontend URL from config service:', error);
      // Fallback to environment variable
      baseUrl = process.env.FRONTEND_URL || 'https://authiqa.com';
    }
  } else {
    console.log('No event provided, using default frontend URL');
    baseUrl = process.env.FRONTEND_URL || 'https://authiqa.com';
  }
  
  const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
  return `${cleanBaseUrl}/reset-password?token=${resetToken}`;
}

export const resetPasswordLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  // Load configuration from the new service
  const config = await getConfig(event);
  
  console.log('[RESET-PASSWORD] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });
  
  console.log('[CONFIG] ResetPasswordLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    frontendUrl: config.FRONTEND_URL,
    userTable: config.USER_DETAILS_TABLE_NAME
  });

  
  try {
    //  Validate request body and extract fields
    const { email, parentPublicKey, updatePasswordPath } = validateRequestBody(event.body);
    
    // Get user by email (and parent public key for child accounts)
    let user;
    if (parentPublicKey) {
      user = await getUserByEmailAndParent(email, parentPublicKey, config.USER_DETAILS_TABLE_NAME);
    } else {
      user = await getUserByEmail(email, config.USER_DETAILS_TABLE_NAME);
    }

    if (!user) {
      return ErrorTypes.USER_NOT_FOUND();
    }

    // Validate account type specific requirements
    await validateAccountRequirements(user, parentPublicKey, config.USER_DETAILS_TABLE_NAME);
    
    // Step 4: Check rate limiting
    checkRateLimiting(user);

    // Step 5: Increment counter after all validations pass
    await incrementResetPasswordRequestCount(user.userID, config.USER_DETAILS_TABLE_NAME);

    // Step 6: Generate new verification token
    const otp = generateOTP();
    const resetToken = generateResetToken(email, otp, parentPublicKey);
    
    //  Generate reset link
    const resetLink = await generateResetLink(user, resetToken, parentPublicKey, updatePasswordPath, event);
    
    // Update user's reset password OTP
    try {
      await updateUserResetPasswordOTP(user.userID, {
        resetPasswordOTP: otp,
        resetPasswordOTPExpiry: Date.now() + OTP_EXPIRY_TIME,
        lastResetPasswordRequestAt: Date.now()
      }, config.USER_DETAILS_TABLE_NAME);
    } catch (error) {
      console.error('Error updating reset password OTP:', error);
      return ErrorTypes.INTERNAL_ERROR();
    }
    
    console.log('Reset Password Request received with paths:', {
      updatePasswordPath,
      email
    });
    
    //  Send password reset email
    try {
      await sendPasswordResetEmail(
        email,
        resetLink,
        user.accountType,
        parentPublicKey,
        updatePasswordPath,
        event  // Pass the event parameter
      );
    } catch (error) {
      console.error('Error sending password reset email:', error);
      return ErrorTypes.EMAIL_SENDING_FAILED();
    }
    
    //  Return success response
    return addCorsHeaders(SuccessResponse(200, {
      message: 'Password reset link has been sent to your email'
    }), event);
  } catch (error) {
    console.error('Error in reset password request:', error);
    
    // If the error is already an APIGatewayProxyResult (from ErrorTypes)
    if (error && typeof error === 'object' && 'statusCode' in error && 'body' in error) {
      return addCorsHeaders(error as APIGatewayProxyResult, event);
    }
    
    // Use config to determine if we should show detailed errors
    if (config.NODE_ENV === 'local') {
      const detailedMessage = error instanceof Error ? error.message : 'An internal server error occurred';
      return addCorsHeaders(ErrorResponse(500, 'INTERNAL_SERVER_ERROR', detailedMessage), event);
    } else {
      return addCorsHeaders(ErrorTypes.INTERNAL_ERROR(), event);
    }
  }

} 
