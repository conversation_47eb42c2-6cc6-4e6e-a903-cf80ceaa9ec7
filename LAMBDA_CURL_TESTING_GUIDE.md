# Lambda Functions cURL Testing Guide

This document provides complete cURL commands for testing all Lambda functions in the SignInSignUp project.

## Base URLs
- **Production**: `https://api.authiqa.com`
- **Staging**: `https://staging-api.authiqa.com`

## Authentication Headers
Most endpoints require one of these authentication methods:
- **JWT Token**: `Authorization: Bearer <token>`
- **Public Key**: `X-Public-Key: <public_key>`
- **Internal Service**: `x-internal-service: <internal_service_key>`

---

## 1. Sign Up Lambda
**Endpoint**: `POST /auth/signup`
**Authentication**: None required

```bash
curl -X POST https://api.authiqa.com/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "parentPublicKey": "APK_parent_key_here",
    "verifyAuthPath": "/custom-verify"
  }'
```

**Required Fields**: `email`, `password`, `username`
**Optional Fields**: `parentPub<PERSON><PERSON>ey`, `verifyAuthPath`

---

## 2. Sign In Lambda
**Endpoint**: `POST /auth/signin`
**Authentication**: None required

```bash
curl -X POST https://api.authiqa.com/auth/signin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "parentPublicKey": "APK_parent_key_here"
  }'
```

**Required Fields**: `email`, `password`
**Optional Fields**: `parentPublicKey` (required for child accounts)

---

## 3. Email Confirmation Lambda
**Endpoint**: `GET /auth/confirm-email`
**Authentication**: None required

```bash
curl -X GET "https://api.authiqa.com/auth/confirm-email?token=ABC123DEF456" \
  -H "Content-Type: application/json"
```

**Required Query Parameters**: `token` (verification token from email)

---

## 4. Resend Confirmation Email Lambda
**Endpoint**: `POST /auth/request-new-confirmation`
**Authentication**: None required

```bash
curl -X POST https://api.authiqa.com/auth/request-new-confirmation \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "parentPublicKey": "APK_parent_key_here",
    "verifyAuthPath": "/custom-verify"
  }'
```

**Required Fields**: `email`
**Optional Fields**: `parentPublicKey`, `verifyAuthPath`

---

## 5. Reset Password Lambda
**Endpoint**: `POST /auth/reset-password`
**Authentication**: None required

```bash
curl -X POST https://api.authiqa.com/auth/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "parentPublicKey": "APK_parent_key_here",
    "updatePasswordPath": "/custom-reset"
  }'
```

**Required Fields**: `email`
**Optional Fields**: `parentPublicKey`, `updatePasswordPath`

---

## 6. Update Password Lambda
**Endpoint**: `POST /auth/update-password`
**Authentication**: None required (uses token from reset email)

```bash
curl -X POST https://api.authiqa.com/auth/update-password \
  -H "Content-Type: application/json" \
  -d '{
    "token": "encrypted_reset_token_from_email",
    "password": "NewSecurePass123!"
  }'
```

**Required Fields**: `token`, `password`

---

## 7. Get Organization Details Lambda
**Endpoint**: `GET
 `/auth/organization-details`
**Authentication**: JWT Token 

```bash
# With JWT Token
curl -X GET https://api.authiqa.com/auth/organization-details \
  -H "Authorization: Bearer <jwt_token>"


```

---

## 8. Update Organization Lambda
**Endpoint**: `POST /auth/update-organization`
**Authentication**: JWT Token (parent accounts only)

```bash
curl -X POST https://api.authiqa.com/auth/update-organization \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <jwt_token>" \
  -d '{
    "organizationName": "My Company",
    "organizationUrl": "https://mycompany.com",
    "domainRestrictionEnabled": true,
    "googleSsoConfig": {
      "enabled": true,
      "clientId": "google_client_id",
      "clientSecret": "google_client_secret"
    }
  }'
```

**Optional Fields**: All fields are optional for updates

---

## 9. Promote Account Lambda
**Endpoint**: `POST /auth/promote-account`
**Authentication**: JWT Token (child accounts only)

```bash
curl -X POST https://api.authiqa.com/auth/promote-account \
  -H "Authorization: Bearer <jwt_token>"
```

**Required**: Valid JWT token from child account

---

## 10. Cost Calculator Lambda
**Endpoint**: `GET /parent/cost-analysis`
**Authentication**: JWT Token OR Internal Service Key

```bash
# With JWT Token
curl -X GET https://api.authiqa.com/parent/cost-analysis \
  -H "Authorization: Bearer <jwt_token>"

# With Internal Service Key
curl -X GET https://api.authiqa.com/parent/cost-analysis \
  -H "x-internal-service: <internal_service_key>" \
  -H "x-public-key: <parent_public_key>"
```

**Required**: Parent account authentication

---

## 11. Payment History Lambda
**Endpoint**: `GET /parent/payment-history`
**Authentication**: JWT Token (parent accounts only)

```bash
curl -X GET "https://api.authiqa.com/parent/payment-history?startDate=*************&endDate=*************&limit=20&startKey=%7B%22key%22%3A%22value%22%7D" \
  -H "Authorization: Bearer <jwt_token>"
```

**Optional Query Parameters**:
- `startDate`: Unix timestamp
- `endDate`: Unix timestamp  
- `limit`: Number (default: 10)
- `startKey`: JSON string for pagination

---

## 12. Billing History Lambda
**Endpoint**: `GET /parent/billing-history`
**Authentication**: JWT Token (parent accounts only)

```bash
curl -X GET "https://api.authiqa.com/parent/billing-history?startDate=2023-01&endDate=2023-12&limit=10" \
  -H "Authorization: Bearer <jwt_token>"
```

**Optional Query Parameters**:
- `startDate`: YYYY-MM format
- `endDate`: YYYY-MM format
- `limit`: Number

---

## 13. Payment Initialization Lambda
**Endpoint**: `POST /parent/initialize-payment`
**Authentication**: JWT Token (parent accounts only)

```bash
curl -X POST https://api.authiqa.com/parent/initialize-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <jwt_token>" \
  -d '{
    "amount": 100.00,
    "invoiceEmail": "<EMAIL>"
  }'
```

**Required Fields**: `amount` (number between 0.01 and 999999)
**Optional Fields**: `invoiceEmail`

---

## Environment Variables Needed

For testing, you'll need these values:
- `<jwt_token>`: Obtained from successful sign-in
- `<public_key>`: User's public key (format: APK_...)
- `<internal_service_key>`: From environment variable `INTERNAL_SERVICE_KEY`

## Response Formats

### Success Response
```json
{
  "statusCode": 200,
  "data": { ... }
}
```

### Error Response
```json
{
  "statusCode": 400,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description"
  }
}
```

## Testing Workflow

1. **Sign Up**: Create a parent account
2. **Confirm Email**: Use token from email
3. **Sign In**: Get JWT token
4. **Update Organization**: Set up organization details
5. **Create Child Account**: Sign up with parentPublicKey
6. **Test Other Endpoints**: Use JWT tokens as needed

---

## 14. Google Auth Lambda
**Endpoint**: `POST /auth/google`
**Authentication**: None required

```bash
curl -X POST https://api.authiqa.com/auth/google \
  -H "Content-Type: application/json" \
  -d '{
    "idToken": "google_id_token_here",
    "parentPublicKey": "APK_parent_key_here"
  }'
```

**Required Fields**: `idToken` (Google ID token)
**Optional Fields**: `parentPublicKey` (for child account creation)

---

## Additional Lambda Functions 

### Stripe Webhook Lambda
**Endpoint**: `POST /webhooks/stripe`
**Authentication**: Stripe signature verification

```bash
curl -X POST https://api.authiqa.com/webhooks/stripe \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: <stripe_signature>" \
  -d '<stripe_webhook_payload>'
```

### Monthly Billing Storage Lambda
**Trigger**: Scheduled (cron: 0 0 1 * ? *)
**Authentication**: Internal AWS trigger

This is a scheduled function that runs monthly and cannot be triggered via HTTP.

---

## Error Codes Reference

### Common Error Codes:
- `MISSING_REQUEST_BODY`: Request body is required
- `INVALID_REQUEST_BODY`: Invalid JSON in request body
- `MISSING_REQUIRED_FIELDS`: Required fields are missing
- `USER_NOT_FOUND`: User not found
- `INVALID_CREDENTIALS`: Invalid email or password
- `EMAIL_NOT_VERIFIED`: Email must be verified
- `ACCOUNT_LOCKED`: Account locked due to failed attempts
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `UNAUTHORIZED`: Invalid or missing authentication
- `INTERNAL_SERVER_ERROR`: Server error occurred

### Authentication Specific:
- `INVALID_TOKEN`: JWT token is invalid or expired
- `MISSING_PARENT_PUBLIC_KEY`: Parent public key required
- `INVALID_PARENT_PUBLIC_KEY_FORMAT`: Invalid public key format
- `PARENT_ACCOUNT_INACTIVE`: Parent account has insufficient balance

---

## Testing Tips

1. **Rate Limiting**: Some endpoints have cooldown periods (5 minutes for password reset, verification emails)

2. **Token Expiry**: JWT tokens expire, you'll need to sign in again to get fresh tokens

3. **Environment Detection**: The system detects staging vs production based on the host header

4. **CORS**: All endpoints support CORS for web browser testing

5. **Validation**: Password must meet complexity requirements (uppercase, lowercase, number, special char, 8+ chars)

6. **Public Key Format**: Must start with "APK_" and be properly formatted

---



## Sample Test Sequence

```bash
# 1. Sign up parent account
curl -X POST https://api.authiqa.com/auth/signup \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"SecurePass123!","username":"parentuser"}'

# 2. Confirm email (check email for token)
curl -X GET "https://api.authiqa.com/auth/confirm-email?token=ABC123DEF456"

# 3. Sign in to get JWT token
curl -X POST https://api.authiqa.com/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"SecurePass123!"}'

# 4. Update organization (use JWT from step 3)
curl -X POST https://api.authiqa.com/auth/update-organization \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <jwt_token>" \
  -d '{"organizationName":"Test Org","organizationUrl":"https://test.com"}'

# 5. Get cost analysis
curl -X GET https://api.authiqa.com/parent/cost-analysis \
  -H "Authorization: Bearer <jwt_token>"
```
