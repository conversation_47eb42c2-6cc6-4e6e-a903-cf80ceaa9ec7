import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ErrorTypes } from '../shared/errorTypes';
import { SuccessResponse, ErrorResponse } from '../shared/responseUtils';
import { getUserByEmail, updateUserVerificationToken, incrementResendEmailCount, getUserByEmailAndParent, getUserByPublicKey } from '../shared/database/userOperations';
import { sendVerificationEmail } from '../shared/emailService';
import { generateVerificationToken } from '../shared/verification/tokenGenerator';
import { validateEmail } from '../shared/validation/userValidation';
import { VERIFICATION_RESEND_COOLDOWN } from '../shared/constants';
import { addCorsHeaders } from '../shared/corsHandler';
import { isValidPublicKeyFormat } from '../shared/utils/apiKeyUtils';
import { getConfig } from '../shared/services/configService';

interface ResendRequest {
  email: string;
  parentPublicKey?: string;
  verifyAuthPath?: string;
}

/**
 * Validates the request body and extracts the required fields
 */
function validateRequestBody(body: string | null): ResendRequest {
  if (!body) {
    throw ErrorTypes.MISSING_REQUEST_BODY();
  }

  let requestBody;
  try {
    requestBody = JSON.parse(body);
  } catch {
    throw ErrorTypes.INVALID_REQUEST_BODY();
  }

  const { email, parentPublicKey, verifyAuthPath } = requestBody;
  if (!email) {
    throw ErrorTypes.MISSING_REQUIRED_FIELDS();
  }

  // Validate email format
  const emailValidation = validateEmail(email);
  if (!emailValidation.isValid) {
    throw ErrorTypes.INVALID_EMAIL_FORMAT();
  }

  // Validate parent public key format if provided
  if (parentPublicKey && !isValidPublicKeyFormat(parentPublicKey)) {
    throw ErrorResponse(400, 'INVALID_PARENT_PUBLIC_KEY_FORMAT', 'Invalid PUBLIC key format');
  }

  return { email, parentPublicKey, verifyAuthPath };
}

/**
 * Validates account type specific requirements
 */
async function validateAccountTypeRequirements(user: any, parentPublicKey?: string, tableName?: string) {
  if (user.accountType === 'child') {
    // Child account validations
    if (!parentPublicKey) {
      throw ErrorResponse(400, 'MISSING_PARENT_PUBLIC_KEY', 'Parent PUBLIC key is required for child accounts');
    }
    if (user.parentAccount !== parentPublicKey) {
      throw ErrorResponse(401, 'INVALID_PARENT_PUBLIC_KEY', 'Invalid parent PUBLIC key');
    }
    
    // Check parent account status - pass the table name parameter
    console.log(`Looking up parent user with key ${user.parentAccount} in table ${tableName || 'default'}`);
    const parentUser = await getUserByPublicKey(user.parentAccount, tableName);
    
    console.log('Parent user lookup result:', {
      userFound: !!parentUser,
      tableName: tableName,
      parentBalance: parentUser ? parentUser.availableBalance : 'N/A'
    });
    
    if (!parentUser || parentUser.availableBalance === undefined || parentUser.availableBalance <= 0) {
      throw ErrorResponse(403, 'PARENT_ACCOUNT_INACTIVE', 'Parent account has insufficient balance for child operations');
    }
  } else {
    // Parent account validations
    if (parentPublicKey) {
      throw ErrorResponse(400, 'INVALID_REQUEST', 'Parent PUBLIC key should not be provided for parent accounts');
    }
    if (user.parentAccount !== 'ROOT') {
      throw ErrorResponse(401, 'INVALID_CREDENTIALS', 'Invalid credentials');
    }
  }

  // Check if email is already verified
  if (user.emailVerified) {
    throw ErrorTypes.EMAIL_ALREADY_VERIFIED();
  }
}

/**
 * Checks rate limiting for verification email resends
 */
function checkRateLimiting(user: any) {
  const lastTokenSentAt = user.lastVerificationTokenSentAt || 0;
  const timeSinceLastToken = Date.now() - lastTokenSentAt;
  
  if (timeSinceLastToken < VERIFICATION_RESEND_COOLDOWN) {
    const remainingTime = Math.ceil((VERIFICATION_RESEND_COOLDOWN - timeSinceLastToken) / 1000);
    const response = ErrorTypes.RATE_LIMIT_EXCEEDED(
      `Please wait ${remainingTime} seconds before requesting a new verification link`
    );
    
    // Add rate limit headers
    response.headers = {
      ...response.headers,
      'X-RateLimit-Limit': '1',
      'X-RateLimit-Remaining': '0',
      'X-RateLimit-Reset': Math.ceil(Date.now() + (VERIFICATION_RESEND_COOLDOWN - timeSinceLastToken))
    };
    
    throw response;
  }
}

export const resendConfirmationEmailLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  // Load configuration from the new service
  const config = await getConfig(event);
  
  console.log('[RESEND-CONFIRMATION] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });
  
  console.log('[CONFIG] ResendConfirmationEmailLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    frontendUrl: config.FRONTEND_URL,
    userTable: config.USER_DETAILS_TABLE_NAME
  });
  
  console.log('Starting resend confirmation process:', {
    hasBody: !!event.body,
    environment: config.NODE_ENV,
    apiVersion: '2.0'
  });

  try {
    //  Validate request and extract fields
    const { email, parentPublicKey, verifyAuthPath } = validateRequestBody(event.body);
    
    // Get user by email (and parent public key for child accounts)
    let user;
    if (parentPublicKey) {
      user = await getUserByEmailAndParent(email, parentPublicKey, config.USER_DETAILS_TABLE_NAME);
    } else {
      user = await getUserByEmail(email, config.USER_DETAILS_TABLE_NAME);
    }

    if (!user) {
      return ErrorTypes.USER_NOT_FOUND();
    }

    // Validate account type specific requirements - pass the table name
    await validateAccountTypeRequirements(user, parentPublicKey, config.USER_DETAILS_TABLE_NAME);

    // Step 4: Check rate limiting
    checkRateLimiting(user);

    // Step 5: Increment counter after all validations pass
    await incrementResendEmailCount(user.userID, config.USER_DETAILS_TABLE_NAME);

    // Step 6: Generate new verification token
    const verificationToken = generateVerificationToken();
    if (!/^[0-9A-Z]{12}$/.test(verificationToken)) {
      console.error('Generated token format invalid:', verificationToken);
      return ErrorTypes.INTERNAL_ERROR();
    }

    // Step 7: Update user and send email
    try {
      await updateUserVerificationToken(user.userID, verificationToken, config.USER_DETAILS_TABLE_NAME);
      
      console.log('Resend Confirmation Request received with paths:', {
        verifyAuthPath,
        email
      });
      
      await sendVerificationEmail(
        email, 
        verificationToken,
        user.accountType,
        user.accountType === 'child' ? user.parentAccount : undefined,
        verifyAuthPath,
        event  // Pass the event to use configuration service
      );
      
      return addCorsHeaders(SuccessResponse(200, {
        message: 'A new verification link has been sent to your email'
      }), event);
    } catch (error) {
      console.error('Error in resend confirmation process:', error);
      return addCorsHeaders(ErrorTypes.INTERNAL_ERROR(), event);
    }
  } catch (error) {
    console.error('Error in resend confirmation lambda:', error);
    
    // If the error is already an APIGatewayProxyResult (from ErrorTypes)
    if (error && typeof error === 'object' && 'statusCode' in error && 'body' in error) {
      return addCorsHeaders(error as APIGatewayProxyResult, event);
    }
    
    // Use config to determine if we should show detailed errors
    if (config.NODE_ENV === 'local') {
      const detailedMessage = error instanceof Error ? error.message : 'An internal server error occurred';
      return addCorsHeaders(ErrorResponse(500, 'INTERNAL_SERVER_ERROR', detailedMessage), event);
    } else {
      return addCorsHeaders(ErrorTypes.INTERNAL_ERROR(), event);
    }
  }
}
 
