import axios from 'axios';
import { Environment } from './environment';
import { APIGatewayProxyEvent } from 'aws-lambda';

export class NotificationService {
  /**
   * Sends a notification to Tel<PERSON>ram about a new signup
   */
  public static async sendSignupNotification(
    username: string, 
    email: string,
    event?: APIGatewayProxyEvent
  ): Promise<void> {
    try {
      // Get Telegram credentials directly from environment variables
      const botToken = process.env.TELEGRAM_BOT_TOKEN;
      const chatId = process.env.TELEGRAM_CHAT_ID;
      
      if (!botToken || !chatId) {
        console.log('Telegram notification skipped: Missing bot token or chat ID');
        return;
      }

      const message = `🎉 New signup!\nUsername: ${username}\nEmail: ${email}`;
      const url = `https://api.telegram.org/bot${botToken}/sendMessage`;
      
      await axios.post(url, {
        chat_id: chatId,
        text: message,
        parse_mode: 'HTML'
      });
      
      console.log('Telegram notification sent successfully');
    } catch (error) {
      console.error('Failed to send Telegram notification:', error);
      // Non-blocking, so we just log the error
    }
  }
}
