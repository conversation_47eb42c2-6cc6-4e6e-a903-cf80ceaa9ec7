import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { SuccessResponse, ErrorResponse } from '../shared/responseUtils';
import { ErrorTypes } from '../shared/errorTypes';
import { validateOrganizationName, validateOrganizationUrl, validateAuthUrls } from '../shared/validation/organizationValidation';
import { getUserByEmail, updateOrganization } from '../shared/database/userOperations';
import { addCorsHeaders } from '../shared/corsHandler';
import { verifyToken } from '../shared/utils/tokenUtils';
import { generateDefaultAuthUrls } from '../shared/utils/authUrlUtils';
import { getConfig } from '../shared/services/configService';
import { encrypt } from '../shared/utils/encryptionUtils';

export const updateOrganizationLambda = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  // Load configuration from the service
  const config = await getConfig(event);
  
  console.log('[UPDATE-ORGANIZATION] Lambda invoked with headers:', {
    host: event.headers.Host || event.headers.host,
    origin: event.headers.Origin || event.headers.origin
  });
  
  console.log('[CONFIG] UpdateOrganizationLambda using configuration service', {
    environment: event.headers.host?.includes('staging') ? 'staging' : 'live',
    userTable: config.USER_DETAILS_TABLE_NAME
  });

  try {
    // Validate request body
    if (!event.body) {
      return addCorsHeaders(ErrorTypes.MISSING_REQUEST_BODY(), event);
    }

    let requestBody;
    try {
      requestBody = JSON.parse(event.body);
    } catch {
      return addCorsHeaders(ErrorTypes.INVALID_REQUEST_BODY(), event);
    }

    // Get token from authorization header
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) {
      return addCorsHeaders(ErrorTypes.UNAUTHORIZED(), event);
    }

    const token = authHeader.replace('Bearer ', '');
    const decodedToken = await verifyToken(token);
    if (!decodedToken) {
      return addCorsHeaders(ErrorTypes.UNAUTHORIZED(), event);
    }

    // Get user by email from decoded token with table name from config
    const user = await getUserByEmail(decodedToken.email, config.USER_DETAILS_TABLE_NAME);
    console.log('User Lookup Result:', {
      found: !!user,
      accountType: user?.accountType,
      parentAccount: user?.parentAccount,
      hasAuthUrls: !!user?.authUrls,  // Check if authUrls exist
      email: decodedToken.email
    });

    if (!user) {
      return addCorsHeaders(ErrorTypes.USER_NOT_FOUND(), event);
    }

    // Check if user is a parent account (both conditions must be true)
    if (user.accountType !== 'parent' || user.parentAccount !== 'ROOT') {
      return addCorsHeaders(ErrorTypes.ORGANIZATION.UNAUTHORIZED_ACCESS(), event);
    }

    const { organizationName, organizationUrl, authUrls, domainRestrictionEnabled, googleSsoConfig } = requestBody;
    
    // Log domain restriction toggle if present
    if (domainRestrictionEnabled !== undefined) {
      console.log(`Domain restriction toggle request received: ${domainRestrictionEnabled}`);
    }

    if (googleSsoConfig) {
        if (typeof googleSsoConfig.enabled !== 'boolean') {
            return addCorsHeaders(ErrorResponse(400, 'INVALID_INPUT', 'googleSsoConfig.enabled must be a boolean.'), event);
        }
        if (googleSsoConfig.enabled) {
            if (!googleSsoConfig.clientId || !googleSsoConfig.clientSecret) {
                return addCorsHeaders(ErrorResponse(400, 'INVALID_INPUT', 'clientId and clientSecret are required when enabling Google SSO.'), event);
            }
            // Encrypt the secret before storing
            googleSsoConfig.clientSecret = encrypt(googleSsoConfig.clientSecret);
        } else {
            // Do NOT clear clientId or clientSecret; just set enabled to false and leave credentials untouched
            // No action needed here
        }
    }
    
    // Case 1: Initial Setup
    if (organizationName && organizationUrl) {
      // Validate organization name
      const nameValidation = validateOrganizationName(organizationName);
      if (!nameValidation.isValid) {
        return addCorsHeaders(ErrorTypes.ORGANIZATION.INVALID_ORGANIZATION_NAME(nameValidation.message), event);
      }

      // Validate organization URL
      const urlValidation = validateOrganizationUrl(organizationUrl);
      if (!urlValidation.isValid) {
        return addCorsHeaders(ErrorTypes.ORGANIZATION.INVALID_ORGANIZATION_URL(urlValidation.message), event);
      }

      // Auto-generate auth URLs based on organization URL
      const generatedAuthUrls = generateDefaultAuthUrls(organizationUrl);

    // Update organization details with generated auth URLs and table name from config
    // Default domainRestrictionEnabled to true if not specified
    const restrictionEnabled = domainRestrictionEnabled !== undefined ? domainRestrictionEnabled : true;
    await updateOrganization(
      user.userID, 
      organizationName, 
      organizationUrl, 
      generatedAuthUrls, 
      restrictionEnabled,
      config.USER_DETAILS_TABLE_NAME,
      googleSsoConfig
    );

      return addCorsHeaders(SuccessResponse(200, {
        message: 'Organization details updated successfully',
        data: {
          organizationName,
          organizationUrl,
          authUrls: generatedAuthUrls,
          domainRestrictionEnabled: restrictionEnabled
        }
      }), event);
    }

    // Case 2: Individual Auth URL Update
    if (authUrls) {
      if (!user.organizationUrl) {
        return addCorsHeaders(ErrorTypes.ORGANIZATION.INVALID_ORGANIZATION_URL('Organization URL not set'), event);
      }

      // Validate the provided auth URLs against the user's existing organization URL
      const authUrlsValidation = validateAuthUrls(authUrls, user.organizationUrl);
      if (!authUrlsValidation.isValid) {
        return addCorsHeaders(ErrorTypes.INVALID_AUTH_URLS(authUrlsValidation.message), event);
      }

      // Merge with existing auth URLs to update only the provided ones
      const updatedAuthUrls = {
        ...user.authUrls,
        ...authUrls
      };

      // Update only auth URLs, passing undefined for organizationName and organizationUrl
      await updateOrganization(user.userID, undefined, undefined, updatedAuthUrls, domainRestrictionEnabled, config.USER_DETAILS_TABLE_NAME, googleSsoConfig);

      return addCorsHeaders(SuccessResponse(200, {
        message: 'Auth URLs updated successfully',
        data: {
          authUrls: updatedAuthUrls,
          ...(domainRestrictionEnabled !== undefined && { domainRestrictionEnabled })
        }
      }), event);
    }

    // Case 3: Domain Restriction Toggle Only
    if (domainRestrictionEnabled !== undefined) {
      console.log(`Updating domain restriction setting to: ${domainRestrictionEnabled}`);
      
      // Update only the domain restriction setting
      await updateOrganization(user.userID, undefined, undefined, undefined, domainRestrictionEnabled, config.USER_DETAILS_TABLE_NAME, googleSsoConfig);

      return addCorsHeaders(SuccessResponse(200, {
        message: 'Domain restriction setting updated successfully',
        data: {
          domainRestrictionEnabled
        }
      }), event);
    }

    // Case 4: Google SSO Config Only Update
    if (googleSsoConfig) {
      // Fetch the existing config
      const existingConfig = user.googleSsoConfig || { enabled: false, clientId: '', clientSecret: '' };

      // If only the 'enabled' field is present, update just that
      if (
        Object.keys(googleSsoConfig).length === 1 &&
        googleSsoConfig.hasOwnProperty('enabled')
      ) {
        const mergedConfig = {
          ...existingConfig,
          enabled: googleSsoConfig.enabled
        };
        await updateOrganization(
          user.userID,
          undefined,
          undefined,
          undefined,
          undefined,
          config.USER_DETAILS_TABLE_NAME,
          mergedConfig
        );
        return addCorsHeaders(SuccessResponse(200, {
          message: 'Google SSO toggle updated successfully',
          data: { googleSsoConfig: mergedConfig }
        }), event);
      }

      // Otherwise, merge and update as before
      const mergedConfig = {
        ...existingConfig,
        ...googleSsoConfig,
      };
      if (googleSsoConfig.clientSecret) {
        mergedConfig.clientSecret = encrypt(googleSsoConfig.clientSecret);
      }
      await updateOrganization(
        user.userID,
        undefined,
        undefined,
        undefined,
        undefined,
        config.USER_DETAILS_TABLE_NAME,
        mergedConfig
      );
      return addCorsHeaders(SuccessResponse(200, {
        message: 'Google SSO config updated successfully',
        data: { googleSsoConfig: mergedConfig }
      }), event);
    }

    return addCorsHeaders(ErrorTypes.MISSING_REQUIRED_FIELDS(), event);

  } catch (error) {
    console.error('Error in updateOrganizationLambda:', error);
    return addCorsHeaders(ErrorTypes.INTERNAL_ERROR(), event);
  }
};
 
